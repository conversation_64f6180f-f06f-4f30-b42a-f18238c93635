import type {
  AnchorHTMLAttributes,
  ButtonHTMLAttributes,
  PropsWithChildren,
  Ref,
} from "react"
import { cva } from "class-variance-authority"
import classNames from "classnames"

import styles from "./button.module.css"
import type { ButtonProps } from "./ButtonProps"

const buttonVariants = cva(styles.buttonRoot, {
  variants: {
    variant: {
      filled: styles.buttonFilled,
      outline: styles.buttonOutline,
      text: styles.buttonText,
    },
    size: {
      large: styles.buttonLarge,
      small: styles.buttonSmall,
    },
    color: {
      primary: styles.buttonPrimary,
      negative: styles.buttonNegative,
    },
  },
  defaultVariants: {
    variant: "filled",
    size: "large",
    color: "primary",
  },
})

export function Button({
  children,
  className,
  fullWidth,
  startDecorator,
  endDecorator,
  variant = "filled",
  size = "large",
  color = "primary",
  ref,
  ...buttonProps
}: PropsWithChildren<ButtonProps>) {
  const buttonVariantStyles = buttonVariants({
    variant,
    size,
    color,
  })

  const baseStyles = classNames(
    "ApolloButton-root",
    buttonVariantStyles,
    {
      [styles.buttonFullWidth]: fullWidth,
      [styles.buttonWithDecorators]: startDecorator || endDecorator,
    },
    className
  )

  if ("href" in buttonProps) {
    const anchorProps = buttonProps as AnchorHTMLAttributes<HTMLAnchorElement>
    return (
      <a
        {...anchorProps}
        ref={ref as Ref<HTMLAnchorElement>}
        className={baseStyles}
      >
        {startDecorator && <span>{startDecorator}</span>}
        {startDecorator || endDecorator ? (
          <span className={styles.buttonWithDecoratorLabel}>{children}</span>
        ) : (
          children
        )}
        {endDecorator && <span>{endDecorator}</span>}
      </a>
    )
  }

  return (
    <button
      {...(buttonProps as ButtonHTMLAttributes<HTMLButtonElement>)}
      ref={ref as Ref<HTMLButtonElement>}
      className={baseStyles}
    >
      {startDecorator && <span>{startDecorator}</span>}
      {startDecorator || endDecorator ? (
        <span className={styles.buttonWithDecoratorLabel}>{children}</span>
      ) : (
        children
      )}
      {endDecorator && <span>{endDecorator}</span>}
    </button>
  )
}
